name: Audio_File_Management
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: audio_vault_app
    restart: always
    ports:
      - ${PORT}:${PORT}
    depends_on:
      postgres:
        condition: service_healthy
        restart: true
    networks:
      - audio_network
    volumes:
      - uploads:/app/uploads
      - logs:/app/logs
    environment:
      # Frontend variables
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      # Backend variables
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DBNAME=${DB_DBNAME}
      - DB_DIALECT=${DB_DIALECT}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      # Subsidiary AWS Credentials
      - SPECTRUM_AWS_ACCESS_KEY_ID=${SPECTRUM_AWS_ACCESS_KEY_ID}
      - SPECTRUM_AWS_SECRET_ACCESS_KEY=${SPECTRUM_AWS_SECRET_ACCESS_KEY}
      - SPECTRUM_AWS_REGION=${SPECTRUM_AWS_REGION}
      - SPECTRUM_S3_BUCKET=${SPECTRUM_S3_BUCKET}
      - FANIKIWA_AWS_ACCESS_KEY_ID=${FANIKIWA_AWS_ACCESS_KEY_ID}
      - FANIKIWA_AWS_SECRET_ACCESS_KEY=${FANIKIWA_AWS_SECRET_ACCESS_KEY}
      - FANIKIWA_AWS_REGION=${FANIKIWA_AWS_REGION}
      - FANIKIWA_S3_BUCKET=${FANIKIWA_S3_BUCKET}
      - PREMIER_UG_AWS_ACCESS_KEY_ID=${PREMIER_UG_AWS_ACCESS_KEY_ID}
      - PREMIER_UG_AWS_SECRET_ACCESS_KEY=${PREMIER_UG_AWS_SECRET_ACCESS_KEY}
      - PREMIER_UG_AWS_REGION=${PREMIER_UG_AWS_REGION}
      - PREMIER_UG_S3_BUCKET=${PREMIER_UG_S3_BUCKET}
      - PLATINUM_UG_AWS_ACCESS_KEY_ID=${PLATINUM_UG_AWS_ACCESS_KEY_ID}
      - PLATINUM_UG_AWS_SECRET_ACCESS_KEY=${PLATINUM_UG_AWS_SECRET_ACCESS_KEY}
      - PLATINUM_UG_AWS_REGION=${PLATINUM_UG_AWS_REGION}
      - PLATINUM_UG_S3_BUCKET=${PLATINUM_UG_S3_BUCKET}
      - PREMIER_KE_AWS_ACCESS_KEY_ID=${PREMIER_KE_AWS_ACCESS_KEY_ID}
      - PREMIER_KE_AWS_SECRET_ACCESS_KEY=${PREMIER_KE_AWS_SECRET_ACCESS_KEY}
      - PREMIER_KE_AWS_REGION=${PREMIER_KE_AWS_REGION}
      - PREMIER_KE_S3_BUCKET=${PREMIER_KE_S3_BUCKET}
      - MOMENTUM_AWS_ACCESS_KEY_ID=${MOMENTUM_AWS_ACCESS_KEY_ID}
      - MOMENTUM_AWS_SECRET_ACCESS_KEY=${MOMENTUM_AWS_SECRET_ACCESS_KEY}
      - MOMENTUM_AWS_REGION=${MOMENTUM_AWS_REGION}
      - MOMENTUM_S3_BUCKET=${MOMENTUM_S3_BUCKET}
      - PLATINUM_KE_AWS_ACCESS_KEY_ID=${PLATINUM_KE_AWS_ACCESS_KEY_ID}
      - PLATINUM_KE_AWS_SECRET_ACCESS_KEY=${PLATINUM_KE_AWS_SECRET_ACCESS_KEY}
      - PLATINUM_KE_AWS_REGION=${PLATINUM_KE_AWS_REGION}
      - PLATINUM_KE_S3_BUCKET=${PLATINUM_KE_S3_BUCKET}
      - PLATINUM_TZ_AWS_ACCESS_KEY_ID=${PLATINUM_TZ_AWS_ACCESS_KEY_ID}
      - PLATINUM_TZ_AWS_SECRET_ACCESS_KEY=${PLATINUM_TZ_AWS_SECRET_ACCESS_KEY}
      - PLATINUM_TZ_AWS_REGION=${PLATINUM_TZ_AWS_REGION}
      - PLATINUM_TZ_S3_BUCKET=${PLATINUM_TZ_S3_BUCKET}
      # Other Configuration
      - CORS_ORIGIN=${CORS_ORIGIN}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE}
      - ALLOWED_AUDIO_FORMATS=${ALLOWED_AUDIO_FORMATS}
      - UPLOAD_PATH=${UPLOAD_PATH}
      - LOG_LEVEL=${LOG_LEVEL}
      - LOG_PATH=${LOG_PATH}


  postgres:
    image: postgres:15
    ports:
      - ${POSTGRES_EXTERNAL_PORT}:5432
    container_name: audio_vault_db
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres:/var/lib/postgresql/data
    networks:
      - audio_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: audio_vault_redis
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis:/data
    ports:
      - ${REDIS_EXTERNAL_PORT}:6379
    networks:
      - audio_network

networks:
  audio_network:
    driver: bridge

volumes:
  postgres:
  redis:
  uploads:
  logs:
